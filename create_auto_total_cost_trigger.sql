-- 创建自动计算入库单总金额的触发器
-- 当入库明细发生增删改时，自动重新计算入库单的总金额

USE [StudentsCMSSP]
GO

-- 删除已存在的触发器（如果有）
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_auto_calculate_stock_in_total_cost')
BEGIN
    DROP TRIGGER tr_auto_calculate_stock_in_total_cost;
    PRINT '已删除旧的触发器';
END

-- 创建新的触发器
CREATE TRIGGER tr_auto_calculate_stock_in_total_cost
ON stock_in_items
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 声明变量存储受影响的入库单ID
    DECLARE @affected_stock_ins TABLE (stock_in_id INT);
    
    -- 收集所有受影响的入库单ID
    INSERT INTO @affected_stock_ins (stock_in_id)
    SELECT DISTINCT stock_in_id FROM inserted
    UNION
    SELECT DISTINCT stock_in_id FROM deleted;
    
    -- 为每个受影响的入库单重新计算总金额
    UPDATE stock_ins
    SET total_cost = ISNULL((
        SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
        FROM stock_in_items sii
        WHERE sii.stock_in_id = stock_ins.id
    ), 0),
    updated_at = GETDATE()
    WHERE id IN (SELECT stock_in_id FROM @affected_stock_ins);
    
    -- 记录日志（可选，用于调试）
    DECLARE @affected_count INT = (SELECT COUNT(*) FROM @affected_stock_ins);
    IF @affected_count > 0
    BEGIN
        PRINT '触发器已自动更新 ' + CAST(@affected_count AS VARCHAR(10)) + ' 个入库单的总金额';
    END
END;

PRINT '✓ 自动计算总金额触发器创建成功';

-- 立即为所有现有入库单计算总金额
PRINT '正在为所有现有入库单计算总金额...';

UPDATE stock_ins
SET total_cost = ISNULL((
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = stock_ins.id
), 0),
updated_at = GETDATE();

DECLARE @updated_count INT = @@ROWCOUNT;
PRINT '✓ 已更新 ' + CAST(@updated_count AS VARCHAR(10)) + ' 个入库单的总金额';

-- 验证结果
PRINT '';
PRINT '验证结果：';
SELECT 
    '入库单103' as 入库单,
    id,
    stock_in_number,
    total_cost as 总金额,
    (SELECT COUNT(*) FROM stock_in_items WHERE stock_in_id = 103) as 明细数量,
    (SELECT SUM(ISNULL(unit_price, 0) * ISNULL(quantity, 0)) FROM stock_in_items WHERE stock_in_id = 103) as 计算总金额
FROM stock_ins 
WHERE id = 103;

-- 显示最近几个入库单的总金额状态
PRINT '';
PRINT '最近入库单总金额状态：';
SELECT TOP 10
    id,
    stock_in_number,
    total_cost,
    status,
    updated_at
FROM stock_ins 
ORDER BY id DESC;

GO
