2025-06-14 22:00:58,675 INFO: 应用启动 - PID: 13060 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-14 22:03:24,044 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 22:04:31,551 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:143]
2025-06-14 22:08:43,858 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 12921.0, 明细: 面粉(600.0公斤)、大米(108.3公斤)、面条(47.0公斤)、豌豆(20.0公斤)、绿豆芽(96.7公斤)、豌豆(100.0公斤)、淀粉(100.0公斤)、豆腐(500.0公斤)、土豆(100.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:43,858 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 8543.5, 明细: 猪肉(100.0公斤)、鸡蛋(4.0公斤)、肉末(100.0公斤)、猪肉(54.3公斤)、鸡蛋(60.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:43,858 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 5529.354, 明细: 胡萝卜(20.0公斤)、韭菜(125.0公斤)、卷心菜(121.7公斤)、包菜(100.0公斤)、葱(10.0公斤)、姜(10.0公斤)、蒜(15.0公斤)、彩椒(24.3公斤)、白菜(121.7公斤)、莲藕(100.0公斤)、西红柿(60.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:43,902 INFO: 财务凭证生成成功: 凭证号=H淀QZ关CDYXXPZ20250614001, 入库单=RK20250614214326, 供应商=绿色农场有限公司, 总金额=26993.85 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1716]
2025-06-14 22:08:44,251 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 12899.4, 明细: 面粉(220.0公斤)、大米(23.0公斤)、面条(47.0公斤)、豌豆(20.0公斤)、绿豆芽(96.7公斤)、豌豆(100.0公斤)、淀粉(10.0公斤)、豆腐(500.0公斤)、土豆(100.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,251 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 8355.65, 明细: 猪肉(100.0公斤)、鸡蛋(4.0公斤)、肉末(34.0公斤)、猪肉(22.97公斤)、鸡蛋(30.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,251 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 6426.7, 明细: 胡萝卜(20.0公斤)、韭菜(125.0公斤)、卷心菜(121.7公斤)、包菜(100.0公斤)、葱(10.0公斤)、姜(10.0公斤)、蒜(15.0公斤)、彩椒(24.3公斤)、白菜(121.7公斤)、莲藕(100.0公斤)、西红柿(60.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,251 INFO: 财务凭证生成成功: 凭证号=H淀QZ关CDYXXPZ20250614002, 入库单=RK20250613220825, 供应商=绿色农场有限公司, 总金额=27681.75 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1716]
2025-06-14 22:08:44,610 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 6232.0, 明细: 面粉(100.0公斤)、面条(47.0公斤)、豌豆(20.0公斤)、豆腐(186.0公斤)、豆角(100.0公斤)、豇豆(100.0公斤)、土豆(100.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,610 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 39972.0, 明细: 猪肉(100.0公斤)、鸡蛋(9.0公斤)、鸡蛋(60.0公斤)、五花肉(500.0公斤)、猪肉(450.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,610 INFO: 食材分类映射: 蔬菜 -> 120101(蔬菜类), 金额: 7618.4, 明细: 胡萝卜(20.0公斤)、西红柿(60.0公斤)、白菜(245.0公斤)、洋葱(20.0公斤)、白菜(500.0公斤)、姜片(5.0公斤)、葱段(3.0公斤)、包菜(100.0公斤)、彩椒(24.3公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1663]
2025-06-14 22:08:44,610 INFO: 财务凭证生成成功: 凭证号=H淀QZ关CDYXXPZ20250614003, 入库单=RK20250613214712, 供应商=绿色农场有限公司, 总金额=53822.40 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1716]
2025-06-14 22:10:21,188 INFO: === 开始生成应付账款 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-14 22:10:21,188 INFO: 请求数据: {'stock_in_id': 100} [in C:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-14 22:10:21,188 INFO: 入库单ID: 100 [in C:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-14 22:10:21,188 INFO: 查询入库单: ID=100, area_id=44 [in C:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-14 22:10:21,204 INFO: 找到入库单: RK20250614214326, 状态: 已入库, 总金额: 26993.85 [in C:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-14 22:10:21,204 INFO: 检查是否已生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-14 22:10:21,214 INFO: 检查入库单状态: 已入库 [in C:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-14 22:10:21,214 INFO: 开始生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-14 22:10:21,215 INFO: 应付账款编号前缀: AP20250614 [in C:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-14 22:10:21,229 INFO: 生成首个应付账款编号: AP20250614001 [in C:\StudentsCMSSP\app\routes\financial\payables.py:357]
2025-06-14 22:10:21,261 INFO: 开始查询会计科目... [in C:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-14 22:10:21,261 INFO: 查询原材料科目 (1201)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-14 22:10:21,268 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-14 22:10:21,268 INFO: 查询应付账款科目 (2001)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-14 22:10:21,270 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-14 22:10:21,271 INFO: 准备凭证明细SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-14 22:10:21,271 INFO: 准备更新入库单SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-14 22:10:21,271 INFO: 开始执行数据库事务... [in C:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-14 22:10:21,271 INFO: 执行应付账款SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-14 22:10:21,279 INFO: 应付账款创建成功，ID: 7 [in C:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-14 22:10:21,279 INFO: 执行财务凭证SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-14 22:10:21,284 INFO: 财务凭证创建成功，ID: 36 [in C:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-14 22:10:21,284 INFO: 创建凭证明细... [in C:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-14 22:10:21,328 INFO: 借方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-14 22:10:21,332 INFO: 贷方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-14 22:10:21,333 INFO: 更新入库单关联信息... [in C:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-14 22:10:21,337 INFO: 入库单更新成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-14 22:10:21,339 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-14 22:10:21,339 INFO: === 应付账款生成成功 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-14 22:10:21,340 INFO: 应付账款ID: 7 [in C:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-14 22:10:21,340 INFO: 财务凭证ID: 36 [in C:\StudentsCMSSP\app\routes\financial\payables.py:514]
2025-06-14 22:10:21,877 INFO: === 开始生成应付账款 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-14 22:10:21,877 INFO: 请求数据: {'stock_in_id': 99} [in C:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-14 22:10:21,877 INFO: 入库单ID: 99 [in C:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-14 22:10:21,877 INFO: 查询入库单: ID=99, area_id=44 [in C:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-14 22:10:21,877 INFO: 找到入库单: RK20250613220825, 状态: 已入库, 总金额: 27681.75 [in C:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-14 22:10:21,877 INFO: 检查是否已生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-14 22:10:21,877 INFO: 检查入库单状态: 已入库 [in C:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-14 22:10:21,877 INFO: 开始生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-14 22:10:21,877 INFO: 应付账款编号前缀: AP20250614 [in C:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-14 22:10:21,877 INFO: 基于最后编号 AP20250614001 生成新编号: AP20250614002 [in C:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-14 22:10:21,893 INFO: 开始查询会计科目... [in C:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-14 22:10:21,893 INFO: 查询原材料科目 (1201)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-14 22:10:21,895 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-14 22:10:21,895 INFO: 查询应付账款科目 (2001)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-14 22:10:21,897 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-14 22:10:21,897 INFO: 准备凭证明细SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-14 22:10:21,897 INFO: 准备更新入库单SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-14 22:10:21,898 INFO: 开始执行数据库事务... [in C:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-14 22:10:21,898 INFO: 执行应付账款SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-14 22:10:21,899 INFO: 应付账款创建成功，ID: 8 [in C:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-14 22:10:21,900 INFO: 执行财务凭证SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-14 22:10:21,901 INFO: 财务凭证创建成功，ID: 37 [in C:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-14 22:10:21,901 INFO: 创建凭证明细... [in C:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-14 22:10:21,903 INFO: 借方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-14 22:10:21,903 INFO: 贷方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-14 22:10:21,903 INFO: 更新入库单关联信息... [in C:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-14 22:10:21,903 INFO: 入库单更新成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-14 22:10:21,908 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-14 22:10:21,908 INFO: === 应付账款生成成功 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-14 22:10:21,908 INFO: 应付账款ID: 8 [in C:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-14 22:10:21,908 INFO: 财务凭证ID: 37 [in C:\StudentsCMSSP\app\routes\financial\payables.py:514]
2025-06-14 22:10:22,442 INFO: === 开始生成应付账款 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:299]
2025-06-14 22:10:22,442 INFO: 请求数据: {'stock_in_id': 95} [in C:\StudentsCMSSP\app\routes\financial\payables.py:303]
2025-06-14 22:10:22,442 INFO: 入库单ID: 95 [in C:\StudentsCMSSP\app\routes\financial\payables.py:306]
2025-06-14 22:10:22,442 INFO: 查询入库单: ID=95, area_id=44 [in C:\StudentsCMSSP\app\routes\financial\payables.py:313]
2025-06-14 22:10:22,442 INFO: 找到入库单: RK20250613214712, 状态: 已入库, 总金额: 53822.40 [in C:\StudentsCMSSP\app\routes\financial\payables.py:323]
2025-06-14 22:10:22,442 INFO: 检查是否已生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:326]
2025-06-14 22:10:22,442 INFO: 检查入库单状态: 已入库 [in C:\StudentsCMSSP\app\routes\financial\payables.py:333]
2025-06-14 22:10:22,442 INFO: 开始生成应付账款... [in C:\StudentsCMSSP\app\routes\financial\payables.py:339]
2025-06-14 22:10:22,442 INFO: 应付账款编号前缀: AP20250614 [in C:\StudentsCMSSP\app\routes\financial\payables.py:344]
2025-06-14 22:10:22,442 INFO: 基于最后编号 AP20250614002 生成新编号: AP20250614003 [in C:\StudentsCMSSP\app\routes\financial\payables.py:354]
2025-06-14 22:10:22,458 INFO: 开始查询会计科目... [in C:\StudentsCMSSP\app\routes\financial\payables.py:399]
2025-06-14 22:10:22,459 INFO: 查询原材料科目 (1201)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:401]
2025-06-14 22:10:22,461 INFO: 找到原材料科目: 原材料 (ID: 206) [in C:\StudentsCMSSP\app\routes\financial\payables.py:411]
2025-06-14 22:10:22,461 INFO: 查询应付账款科目 (2001)... [in C:\StudentsCMSSP\app\routes\financial\payables.py:415]
2025-06-14 22:10:22,465 INFO: 找到应付账款科目: 应付账款 (ID: 220) [in C:\StudentsCMSSP\app\routes\financial\payables.py:425]
2025-06-14 22:10:22,466 INFO: 准备凭证明细SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:437]
2025-06-14 22:10:22,466 INFO: 准备更新入库单SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:440]
2025-06-14 22:10:22,467 INFO: 开始执行数据库事务... [in C:\StudentsCMSSP\app\routes\financial\payables.py:443]
2025-06-14 22:10:22,467 INFO: 执行应付账款SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:447]
2025-06-14 22:10:22,473 INFO: 应付账款创建成功，ID: 9 [in C:\StudentsCMSSP\app\routes\financial\payables.py:457]
2025-06-14 22:10:22,477 INFO: 执行财务凭证SQL... [in C:\StudentsCMSSP\app\routes\financial\payables.py:460]
2025-06-14 22:10:22,481 INFO: 财务凭证创建成功，ID: 38 [in C:\StudentsCMSSP\app\routes\financial\payables.py:470]
2025-06-14 22:10:22,482 INFO: 创建凭证明细... [in C:\StudentsCMSSP\app\routes\financial\payables.py:474]
2025-06-14 22:10:22,485 INFO: 借方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:484]
2025-06-14 22:10:22,491 INFO: 贷方明细创建成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:494]
2025-06-14 22:10:22,491 INFO: 更新入库单关联信息... [in C:\StudentsCMSSP\app\routes\financial\payables.py:499]
2025-06-14 22:10:22,491 INFO: 入库单更新成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:506]
2025-06-14 22:10:22,495 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\routes\financial\payables.py:510]
2025-06-14 22:10:22,495 INFO: === 应付账款生成成功 === [in C:\StudentsCMSSP\app\routes\financial\payables.py:512]
2025-06-14 22:10:22,495 INFO: 应付账款ID: 9 [in C:\StudentsCMSSP\app\routes\financial\payables.py:513]
2025-06-14 22:10:22,495 INFO: 财务凭证ID: 38 [in C:\StudentsCMSSP\app\routes\financial\payables.py:514]
2025-06-14 22:10:42,387 INFO: 批量生成明细账请求: user_area=44, request_data={'year': 2025, 'month': 6, 'subject_ids': []} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:132]
2025-06-14 22:10:42,387 INFO: 获取有发生额的科目: area_id=44, year=2025, month=6 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:153]
2025-06-14 22:10:42,387 INFO: 找到有发生额的科目数量: 4 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:155]
2025-06-14 22:10:42,387 INFO: 生成明细账: subject_id=206 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-14 22:10:42,463 INFO: 生成明细账: subject_id=207 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-14 22:10:42,480 INFO: 生成明细账: subject_id=208 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-14 22:10:42,494 INFO: 生成明细账: subject_id=220 [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:166]
2025-06-14 22:10:42,514 INFO: 批量生成明细账完成: {'success': True, 'message': '批量生成完成，成功 4 个科目', 'results': [{'subject_id': 206, 'result': {'success': True, 'message': '成功生成 1201-原材料 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': 140550.4, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 207, 'result': {'success': True, 'message': '成功生成 120101-蔬菜类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 19574.449999999997, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 208, 'result': {'success': True, 'message': '成功生成 120102-肉类 2025年6月明细账', 'records_count': 5, 'opening_balance': 0.0, 'closing_balance': 56871.15, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}, {'subject_id': 220, 'result': {'success': True, 'message': '成功生成 2001-应付账款 2025年6月明细账', 'records_count': 8, 'opening_balance': 0.0, 'closing_balance': -216996.0, 'save_result': {'saved': False, 'message': '明细账未持久化保存，仅实时生成'}}}]} [in C:\StudentsCMSSP\app\routes\financial\ledgers.py:186]
