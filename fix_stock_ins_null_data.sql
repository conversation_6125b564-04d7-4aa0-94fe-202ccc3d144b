-- 修复 stock_ins 表中的 NULL 数据
-- 特别针对 area_id 和 supplier_id 为 NULL 的记录
-- 执行前建议备份数据库

USE [StudentsCMSSP]
GO

-- 开始事务以确保数据一致性
BEGIN TRANSACTION;

PRINT '开始修复 stock_ins 表中的 NULL 数据...'

-- 显示修复前的数据统计
PRINT '修复前数据统计:'
SELECT
    '修复前统计' as 统计项目,
    COUNT(*) as 入库单总数,
    COUNT(area_id) as 有area_id的记录数,
    COUNT(supplier_id) as 有supplier_id的记录数,
    COUNT(CASE WHEN total_cost > 0 THEN 1 END) as 有成本的记录数,
    COUNT(CASE WHEN area_id IS NULL THEN 1 END) as area_id为NULL的记录数,
    COUNT(CASE WHEN supplier_id IS NULL THEN 1 END) as supplier_id为NULL的记录数
FROM stock_ins;

-- 1. 修复 area_id 为 NULL 的记录（从仓库表获取）
UPDATE si 
SET area_id = w.area_id
FROM stock_ins si
INNER JOIN warehouses w ON si.warehouse_id = w.id
WHERE si.area_id IS NULL;

DECLARE @updated_area_count INT = @@ROWCOUNT;
PRINT '✓ 修复了 ' + CAST(@updated_area_count AS VARCHAR(10)) + ' 条记录的 area_id'

-- 2. 修复采购入库的 supplier_id（从采购订单获取）
UPDATE si
SET supplier_id = po.supplier_id
FROM stock_ins si
INNER JOIN purchase_orders po ON si.purchase_order_id = po.id
WHERE si.supplier_id IS NULL 
AND si.purchase_order_id IS NOT NULL
AND po.supplier_id IS NOT NULL;

DECLARE @updated_supplier_from_po INT = @@ROWCOUNT;
PRINT '✓ 从采购订单修复了 ' + CAST(@updated_supplier_from_po AS VARCHAR(10)) + ' 条记录的 supplier_id'

-- 3. 修复其他入库的 supplier_id（从入库明细获取第一个供应商）
UPDATE si
SET supplier_id = (
    SELECT TOP 1 sii.supplier_id
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
    AND sii.supplier_id IS NOT NULL
)
FROM stock_ins si
WHERE si.supplier_id IS NULL
AND EXISTS (
    SELECT 1 FROM stock_in_items sii 
    WHERE sii.stock_in_id = si.id 
    AND sii.supplier_id IS NOT NULL
);

DECLARE @updated_supplier_from_items INT = @@ROWCOUNT;
PRINT '✓ 从入库明细修复了 ' + CAST(@updated_supplier_from_items AS VARCHAR(10)) + ' 条记录的 supplier_id'

-- 4. 计算并更新 total_cost（从入库明细计算）
UPDATE si
SET total_cost = ISNULL((
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
), 0)
FROM stock_ins si
WHERE si.total_cost IS NULL OR si.total_cost = 0;

DECLARE @updated_cost_count INT = @@ROWCOUNT;
PRINT '✓ 计算了 ' + CAST(@updated_cost_count AS VARCHAR(10)) + ' 条记录的 total_cost'

-- 5. 显示修复结果统计
SELECT 
    '修复前后对比' as 统计项目,
    COUNT(*) as 入库单总数,
    COUNT(area_id) as 有area_id的记录数,
    COUNT(supplier_id) as 有supplier_id的记录数,
    COUNT(CASE WHEN total_cost > 0 THEN 1 END) as 有成本的记录数,
    COUNT(CASE WHEN area_id IS NULL THEN 1 END) as area_id为NULL的记录数,
    COUNT(CASE WHEN supplier_id IS NULL THEN 1 END) as supplier_id为NULL的记录数
FROM stock_ins;

-- 6. 显示仍然有问题的记录
PRINT '仍然存在问题的记录:'
SELECT
    id,
    stock_in_number,
    stock_in_type,
    warehouse_id,
    area_id,
    supplier_id,
    total_cost,
    status,
    notes
FROM stock_ins
WHERE area_id IS NULL OR (supplier_id IS NULL AND stock_in_type = '采购入库')
ORDER BY id DESC;

PRINT '数据修复完成！'
PRINT '总计修复:'
PRINT '- area_id: ' + CAST(@updated_area_count AS VARCHAR(10)) + ' 条记录'
PRINT '- supplier_id (从采购订单): ' + CAST(@updated_supplier_from_po AS VARCHAR(10)) + ' 条记录'
PRINT '- supplier_id (从入库明细): ' + CAST(@updated_supplier_from_items AS VARCHAR(10)) + ' 条记录'
PRINT '- total_cost: ' + CAST(@updated_cost_count AS VARCHAR(10)) + ' 条记录'

-- 提交事务
COMMIT TRANSACTION;
PRINT '✓ 事务已提交，数据修复成功完成！'

GO
