-- 删除所有平账入库记录
-- 执行前请务必备份数据库！
-- 此操作不可逆转

USE [StudentsCMSSP]
GO

-- 开始事务以确保数据一致性
BEGIN TRANSACTION;

PRINT '开始删除所有平账入库记录...'

-- 显示删除前的统计信息
PRINT '删除前统计信息:'
SELECT 
    '删除前统计' as 统计项目,
    COUNT(*) as 平账入库总数,
    COUNT(CASE WHEN status = '已完成' THEN 1 END) as 已完成数量,
    COUNT(CASE WHEN status = '待审核' THEN 1 END) as 待审核数量,
    SUM(ISNULL(total_cost, 0)) as 总成本
FROM stock_ins 
WHERE stock_in_type = '平账入库';

-- 获取所有平账入库的ID，用于删除相关记录
DECLARE @balance_stock_in_ids TABLE (id INT);
INSERT INTO @balance_stock_in_ids (id)
SELECT id FROM stock_ins WHERE stock_in_type = '平账入库';

DECLARE @balance_count INT = (SELECT COUNT(*) FROM @balance_stock_in_ids);
PRINT '找到 ' + CAST(@balance_count AS VARCHAR(10)) + ' 条平账入库记录需要删除';

-- 1. 删除相关的库存记录（批次号以 BAL- 开头的）
PRINT '正在删除相关库存记录...'
DELETE FROM inventories 
WHERE batch_number LIKE 'BAL-%';

DECLARE @deleted_inventory_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_inventory_count AS VARCHAR(10)) + ' 条相关库存记录';

-- 2. 删除入库明细记录
PRINT '正在删除入库明细记录...'
DELETE FROM stock_in_items 
WHERE stock_in_id IN (SELECT id FROM @balance_stock_in_ids);

DECLARE @deleted_items_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_items_count AS VARCHAR(10)) + ' 条入库明细记录';

-- 3. 删除入库单据关联记录（如果存在）
PRINT '正在删除入库单据关联记录...'
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'stock_in_document_items')
BEGIN
    DELETE FROM stock_in_document_items
    WHERE document_id IN (
        SELECT d.id FROM stock_in_documents d
        INNER JOIN @balance_stock_in_ids b ON d.stock_in_id = b.id
    );
END

DECLARE @deleted_doc_items_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_doc_items_count AS VARCHAR(10)) + ' 条入库单据关联记录';

-- 4. 删除入库单据记录
PRINT '正在删除入库单据记录...'
DELETE FROM stock_in_documents 
WHERE stock_in_id IN (SELECT id FROM @balance_stock_in_ids);

DECLARE @deleted_docs_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_docs_count AS VARCHAR(10)) + ' 条入库单据记录';

-- 5. 删除审计日志中的相关记录
PRINT '正在删除相关审计日志...'
DELETE FROM audit_logs
WHERE resource_type = 'stock_ins'
AND resource_id IN (SELECT id FROM @balance_stock_in_ids);

DECLARE @deleted_audit_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_audit_count AS VARCHAR(10)) + ' 条相关审计日志';

-- 6. 最后删除平账入库主记录
PRINT '正在删除平账入库主记录...'
DELETE FROM stock_ins 
WHERE stock_in_type = '平账入库';

DECLARE @deleted_stock_ins_count INT = @@ROWCOUNT;
PRINT '✓ 删除了 ' + CAST(@deleted_stock_ins_count AS VARCHAR(10)) + ' 条平账入库主记录';

-- 显示删除后的验证信息
PRINT '删除后验证:'
SELECT 
    '删除后验证' as 验证项目,
    COUNT(*) as 剩余平账入库数量
FROM stock_ins 
WHERE stock_in_type = '平账入库';

-- 检查是否还有遗留的BAL-批次号库存
SELECT 
    '遗留BAL批次库存' as 验证项目,
    COUNT(*) as 遗留数量
FROM inventories 
WHERE batch_number LIKE 'BAL-%';

-- 显示删除汇总
PRINT ''
PRINT '删除操作完成汇总:'
PRINT '- 平账入库主记录: ' + CAST(@deleted_stock_ins_count AS VARCHAR(10)) + ' 条'
PRINT '- 入库明细记录: ' + CAST(@deleted_items_count AS VARCHAR(10)) + ' 条'
PRINT '- 相关库存记录: ' + CAST(@deleted_inventory_count AS VARCHAR(10)) + ' 条'
PRINT '- 入库单据记录: ' + CAST(@deleted_docs_count AS VARCHAR(10)) + ' 条'
PRINT '- 单据关联记录: ' + CAST(@deleted_doc_items_count AS VARCHAR(10)) + ' 条'
PRINT '- 审计日志记录: ' + CAST(@deleted_audit_count AS VARCHAR(10)) + ' 条'

-- 提交事务
COMMIT TRANSACTION;
PRINT '✓ 事务已提交，所有平账入库记录删除完成！'

PRINT ''
PRINT '⚠️  重要提醒:'
PRINT '1. 平账入库功能已从系统中完全移除'
PRINT '2. 相关的库存数据已被清理'
PRINT '3. 如需恢复数据，请使用备份进行还原'
PRINT '4. 建议检查库存数据的完整性'

GO
